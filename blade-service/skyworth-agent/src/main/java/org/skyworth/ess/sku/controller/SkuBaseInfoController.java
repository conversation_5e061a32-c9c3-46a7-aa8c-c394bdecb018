/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.sku.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.skyworth.ess.sku.vo.SkuBaseInfoResultVO;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.sku.entity.SkuBaseInfoEntity;
import org.skyworth.ess.sku.vo.SkuBaseInfoVO;
import org.skyworth.ess.sku.excel.SkuBaseInfoExcel;
import org.skyworth.ess.sku.wrapper.SkuBaseInfoWrapper;
import org.skyworth.ess.sku.service.ISkuBaseInfoService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import springfox.documentation.annotations.ApiIgnore;
import java.util.Map;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
 * 物料基础信息表 控制器
 *
 * <AUTHOR>
 * @since 2023-10-31
 */
@RestController
@AllArgsConstructor
@RequestMapping("/skuBaseInfo")
@Api(value = "物料基础信息表", tags = "物料基础信息表接口")
public class SkuBaseInfoController extends BladeController {
	private final ISkuBaseInfoService skuBaseInfoService;

	/**
	 * 物料基础信息表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入skuBaseInfo")
	@PreAuth("hasPermission('agent:skuBaseInfo:detail')")
	public R<SkuBaseInfoVO> detail(SkuBaseInfoEntity skuBaseInfo) {
		SkuBaseInfoEntity detail = skuBaseInfoService.getOne(Condition.getQueryWrapper(skuBaseInfo));
		return R.data(SkuBaseInfoWrapper.build().entityVO(detail));
	}
	/**
	 * 物料基础信息表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入skuBaseInfo")
	@PreAuth("hasPermission('agent:skuBaseInfo:list')")
	public R<IPage<SkuBaseInfoVO>> list(@ApiIgnore @RequestParam Map<String, Object> skuBaseInfo, Query query) {
		IPage<SkuBaseInfoEntity> pages = skuBaseInfoService.page(Condition.getPage(query), Condition.getQueryWrapper(skuBaseInfo, SkuBaseInfoEntity.class));
		return R.data(SkuBaseInfoWrapper.build().pageVO(pages));
	}

	/**
	 * 物料基础信息表 自定义分页
	 */
	@PostMapping("/page/{size}/{current}")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入skuBaseInfo")
	@PreAuth("hasPermission('agent:skuBaseInfo:page')")
	public R<IPage<SkuBaseInfoVO>> page(@RequestBody SkuBaseInfoVO skuBaseInfo, Query query) {
		query.setDescs("id");
		IPage<SkuBaseInfoVO> pages = skuBaseInfoService.selectSkuBaseInfoPage(Condition.getPage(query), skuBaseInfo);
		return R.data(pages);
	}

	/**
	 * 物料基础信息表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入skuBaseInfo")
	@PreAuth("hasPermission('agent:skuBaseInfo:save')")
	public R save(@Valid @RequestBody SkuBaseInfoEntity skuBaseInfo) {
		return skuBaseInfoService.saveSku(skuBaseInfo);
	}

	/**
	 * 物料基础信息表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入skuBaseInfo")
	@PreAuth("hasPermission('agent:skuBaseInfo:update')")
	public R update(@Valid @RequestBody SkuBaseInfoEntity skuBaseInfo) {
		return skuBaseInfoService.updateSku(skuBaseInfo);
	}

	/**
	 * 物料基础信息表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入skuBaseInfo")
	@PreAuth("hasPermission('agent:skuBaseInfo:submit')")
	public R submit(@Valid @RequestBody SkuBaseInfoEntity skuBaseInfo) {
		return R.status(skuBaseInfoService.saveOrUpdate(skuBaseInfo));
	}

	/**
	 * 物料基础信息表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@PreAuth("hasPermission('agent:skuBaseInfo:remove')")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(skuBaseInfoService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-skuBaseInfo")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入skuBaseInfo")
	@PreAuth("hasPermission('agent:skuBaseInfo:export-skuBaseInfo')")
	public void exportSkuBaseInfo(@ApiIgnore @RequestParam Map<String, Object> skuBaseInfo, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<SkuBaseInfoEntity> queryWrapper = Condition.getQueryWrapper(skuBaseInfo, SkuBaseInfoEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(SkuBaseInfo::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(SkuBaseInfoEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<SkuBaseInfoExcel> list = skuBaseInfoService.exportSkuBaseInfo(queryWrapper);
		ExcelUtil.export(response, "物料基础信息表数据" + DateUtil.time(), "物料基础信息表数据表", list, SkuBaseInfoExcel.class);
	}

	@GetMapping("/pull-down")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "物料下拉", notes = "传入skuCode或者skuName")
//	@PreAuth("hasPermission('agent:skuBaseInfo:pull-down')")
	public R<List<SkuBaseInfoResultVO>> pullDown(@RequestParam("skuCodeOrName") String skuCodeOrName) {
		SkuBaseInfoVO vo = new SkuBaseInfoVO();
		vo.setSkuCodeOrName(skuCodeOrName);
		return skuBaseInfoService.pullDown(vo);
	}

	@GetMapping("/base-package-sku-info")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "根据物料包获取物料信息", notes = "传入skuCode或者skuName")
	public R<SkuBaseInfoVO> basePackageSkuInfo(@RequestParam("basePackage") String basePackage) {
		return skuBaseInfoService.basePackageSkuInfo(basePackage);
	}
}
