package org.skyworth.ess.quote.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.skyworth.ess.quote.entity.DeviceQuoteInfo;
import org.skyworth.ess.quote.mapper.DeviceQuoteInfoMapper;
import org.skyworth.ess.quote.service.IDeviceQuoteInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 设备报价信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-26
 */
@Service
public class DeviceQuoteInfoServiceImpl extends ServiceImpl<DeviceQuoteInfoMapper, DeviceQuoteInfo> implements IDeviceQuoteInfoService {

    @Override
    public DeviceQuoteInfo getByOrderId(Long orderId) {
        LambdaQueryWrapper<DeviceQuoteInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceQuoteInfo::getOrderId, orderId);
        return this.getOne(queryWrapper);
    }
}
