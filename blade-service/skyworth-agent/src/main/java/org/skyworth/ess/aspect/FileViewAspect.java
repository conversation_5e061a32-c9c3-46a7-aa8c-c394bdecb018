/*
 * Copyright (c) 2023. Skyworth All rights reserved
 */

package org.skyworth.ess.aspect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.skyworth.ess.additionalInfo.service.IAdditionalInfoService;
import org.springblade.common.utils.CollectionUtils;
import org.springblade.system.entity.AttachmentInfoEntity;
import org.springblade.system.entity.SkyWorthFileEntity;
import org.springblade.system.feign.IAttachmentInfoClient;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentSkipListSet;

/**
 * 操作日志拦截器
 *
 * @description:
 * @author: SDT50545
 * @since: 2023-11-07 17:48
 **/
@Data
@Aspect
@Component
@Slf4j
public class FileViewAspect {

	private static final Map<Long, String> CONCURRENT_HASH_MAP = new ConcurrentHashMap<>();
	private static final Set<Long> SET = new ConcurrentSkipListSet<>();
	private static final String END_WITH_FILE = "BizKey";
	/**
	 * 附件信息
	 */
	private final IAttachmentInfoClient attachmentInfoClient;

	private final IAdditionalInfoService additionalInfoService;

	/**
	 * 处理完请求后执行此处代码
	 */
	@Pointcut("@annotation(org.skyworth.ess.aspect.FileView)")
	public void logPointCut() {
	}

	@Around("logPointCut()")
	public Object doAfterReturning(ProceedingJoinPoint joinPoint) throws Throwable {
		//执行方法
		Object[] args = joinPoint.getArgs();
		Object result = joinPoint.proceed(args);
		if (result == null) {
			return null;
		}
		Class<?> resultClass = result.getClass();
		boolean isSupperFileClass = resultClass.getSuperclass() == SkyWorthFileEntity.class;
		if (!isSupperFileClass) {
			return result;
		}
		JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(result));
		// 解析JSON->Map
		traverseJson(jsonObject, true);
		if (!CollectionUtils.isNullOrEmpty(SET)) {
			List<Long> businessIds = new ArrayList<>(SET);
			// 查询附件
			Map<Long, List<AttachmentInfoEntity>> attachmentMap = attachmentInfoClient.findByBusinessIds(businessIds).getData();
			Map<Long, String> imgDescMap = additionalInfoService.selectAdditionalMapByBusinessIds(businessIds);
			Map<String, List<AttachmentInfoEntity>> attachmentClearnessKeyViewMap = new HashMap<>(0);
			attachmentMap.forEach((key, value) -> {
				if (CONCURRENT_HASH_MAP.containsKey(key)) {
					attachmentClearnessKeyViewMap.put(CONCURRENT_HASH_MAP.get(key), value);
				}
			});
			Map<String, String> imgDescClearnessKeyViewMap = new HashMap<>(0);
			imgDescMap.forEach((key, value) -> {
				if (CONCURRENT_HASH_MAP.containsKey(key)) {
					imgDescClearnessKeyViewMap.put(CONCURRENT_HASH_MAP.get(key), value);
				}
			});
			jsonObject.put("attachmentClearnessKeyViewMap", attachmentClearnessKeyViewMap);
			/*jsonObject.put("attachmentMap", attachmentMap);
			jsonObject.put("imgDescViewMap", imgDescMap);*/
			jsonObject.put("imgDescClearnessKeyViewMap", imgDescClearnessKeyViewMap);
		}
		return JSONObject.parseObject(jsonObject.toJSONString(), resultClass);
	}

	private static void traverseJson(JSONObject jsonObject, Boolean reName) {
		for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
			String key = entry.getKey();
			Object value = entry.getValue();
			if (value == null) {
				continue;
			}
			if (value instanceof JSONObject) {
				traverseJson((JSONObject) value, reName);
			} else if (value instanceof Long) {
				packageNameAndId(key, value, reName);
			} /*else if (value instanceof JSONArray) {
				//traverseJsonArray((JSONArray) value);
			}*/
		}
	}

	private static void packageNameAndId(String key, Object value, Boolean reName) {
		if (key.endsWith(END_WITH_FILE) && value != null) {
			if (reName) {
				CONCURRENT_HASH_MAP.put((Long) value, key);
			}
			SET.add((Long) value);
		}
	}

	private static void traverseJsonArray(JSONArray jsonArray) {
		for (Object o : jsonArray) {
			JSONObject jsonObject = (JSONObject) o;
			traverseJson(jsonObject, false);
		}
	}

}
