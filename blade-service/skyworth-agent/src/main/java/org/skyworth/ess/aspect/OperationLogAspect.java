/*
 * Copyright (c) 2023. Skyworth All rights reserved
 */

package org.skyworth.ess.aspect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.skyworth.ess.company.entity.AgentOperationLogEntity;
import org.skyworth.ess.company.service.IAgentOperationLogService;
import org.skyworth.ess.constant.OperateEnum;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.core.tool.api.ResultCode;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.HandlerMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 操作日志拦截器
 *
 * @description:
 * @author: SDT50545
 * @since: 2023-11-07 17:48
 **/
@Data
@Aspect
@Component
@Slf4j
public class OperationLogAspect {
	/**
	 * 引入日志Service，用于存储数据进数据库
	 */
	private final IAgentOperationLogService operationLogService;

	/**
	 * 配置切入点-xxx代表自定义注解的存放位置，如：com.xiarg.genius.annotation.annotation.Log
	 */
	@Pointcut("@annotation(org.skyworth.ess.aspect.OperationLog)")
	public void logPointCut() {
	}

	/**
	 * 处理完请求后执行此处代码
	 *
	 * @param joinPoint 切点
	 */
	@AfterReturning(pointcut = "@annotation(operationLog)", returning = "jsonResult")
	public void doAfterReturning(JoinPoint joinPoint, OperationLog operationLog, Object jsonResult) {
		handleLog(joinPoint, operationLog, null, jsonResult);
	}

	/**
	 * 如果处理请求时出现异常，在抛出异常后执行此处代码
	 *
	 * @param joinPoint 切点
	 * @param e         异常
	 */
	@AfterThrowing(value = "@annotation(operationLog)", throwing = "e")
	public void doAfterThrowing(JoinPoint joinPoint, OperationLog operationLog, Exception e) {
		handleLog(joinPoint, operationLog, e, null);
	}

	/**
	 * 处理日志数据
	 *
	 * @param joinPoint    j
	 * @param operationLog log
	 * @param e            e
	 * @param jsonResult   j
	 */
	protected void handleLog(final JoinPoint joinPoint, OperationLog operationLog, final Exception e, Object jsonResult) {
		try {
			// 批量删除单独记录日志
			if (operationLog.operate().equals(OperateEnum.DELETE)) {
				batchDeleteLog(joinPoint, operationLog, e, jsonResult);
			} else {
				addOrUpdateLog(joinPoint, operationLog, e, jsonResult);
			}
		} catch (Exception exp) {
			// 记录本地异常日志
			log.error("==前置通知异常==");
			log.error("异常信息:{}", exp.getMessage());
		}
	}

	/**
	 * 批量删除记录日志
	 *
	 * @param joinPoint    入参
	 * @param operationLog 参数
	 * @param e            异常
	 * @param jsonResult   入参
	 * <AUTHOR>
	 * @since 2023/11/14 14:56
	 **/
	private void batchDeleteLog(JoinPoint joinPoint, OperationLog operationLog, Exception e, Object jsonResult) {
		Object[] array = joinPoint.getArgs();
		if (array == null) {
			return;
		}
		String[] jsonArray = new String[0];
		for (Object o : array) {
			JSONObject jsonObject = (JSONObject) JSON.toJSON(o);
			if (jsonObject.containsKey("ids")) {
				jsonArray = jsonObject.getString("ids").split(",");
				break;
			}
		}
		if (jsonArray.length == 0) {
			return;
		}
		List<AgentOperationLogEntity> operationLogEntityList = new ArrayList<>();
		for (Object o : jsonArray) {
			AgentOperationLogEntity operationLogEntity = new AgentOperationLogEntity();
			operationLogEntity.setModelType(operationLog.model().name());
			operationLogEntity.setOperateType(operationLog.operate().getCode());
			operationLogEntity.setBusinessId(o.toString());
			operationLogEntity.setRequestBody(o.toString());
			extractedResponse(e, jsonResult, operationLogEntity);
			operationLogEntityList.add(operationLogEntity);
		}
		operationLogService.saveBatch(operationLogEntityList);
	}

	/**
	 * 设置响应
	 *
	 * @param e                  异常
	 * @param jsonResult         响应
	 * @param operationLogEntity 入参
	 * <AUTHOR>
	 * @since 2023/11/14 15:30
	 **/
	private void extractedResponse(Exception e, Object jsonResult, AgentOperationLogEntity operationLogEntity) {
		if (e != null) {
			operationLogEntity.setStatus(0);
			int length = e.getMessage().length();
			operationLogEntity.setResponseBody(e.getMessage().substring(0, Math.min(length, 512)));
		} else {
			String responseJson = JSON.toJSONString(jsonResult);
			JSONObject j = JSON.parseObject(responseJson);
			int code = j.getInteger("code");
			if (ResultCode.SUCCESS.getCode() != code) {
				operationLogEntity.setStatus(0);
			}
			int length = responseJson.length();
			operationLogEntity.setResponseBody(responseJson.substring(0, Math.min(length, 512)));
		}
	}

	/**
	 * 新增或修改记录日志
	 *
	 * @param joinPoint    入参
	 * @param operationLog 参数
	 * @param e            异常
	 * @param jsonResult   入参
	 * <AUTHOR>
	 * @since 2023/11/14 14:56
	 **/
	private void addOrUpdateLog(JoinPoint joinPoint, OperationLog operationLog, Exception e, Object jsonResult) throws Exception {
		AgentOperationLogEntity operationLogEntity = new AgentOperationLogEntity();
		// *========获取request请求=========*//
		ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		assert requestAttributes != null;
		HttpServletRequest request = requestAttributes.getRequest();
		// 设置请求参数
		setRequestValue(joinPoint, request, operationLogEntity, operationLog.businessIdFiled());
		operationLogEntity.setOperateType(operationLog.operate().getCode());
		operationLogEntity.setModelType(operationLog.model().name());
		// 设置响应参数
		extractedResponse(e, jsonResult, operationLogEntity);
		// 将处理好的日至对象存储进数据库
		operationLogService.save(operationLogEntity);
	}


	/**
	 * 设置入参
	 *
	 * @param joinPoint joinPoint
	 * @param request   request
	 * <AUTHOR>
	 * @since 2023/11/8 10:29
	 **/
	private void setRequestValue(JoinPoint joinPoint, HttpServletRequest request, AgentOperationLogEntity operationLogEntity, String businessIdField) {
		String requestMethod = request.getMethod();
		if (RequestMethod.PUT.name().equals(requestMethod) || RequestMethod.POST.name().equals(requestMethod)) {
			argsArrayToString(joinPoint.getArgs(), operationLogEntity, businessIdField);
		} else {
			Map<?, ?> paramsMap = (Map<?, ?>) request.getAttribute(HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE);
			operationLogEntity.setRequestBody(paramsMap.toString().substring(0, 512));
		}
	}


	/**
	 * 参数解析
	 *
	 * @param paramsArray 参数数组
	 */
	private void argsArrayToString(Object[] paramsArray, AgentOperationLogEntity logEntity, String businessIdField) {
		StringBuilder params = new StringBuilder();
		if (paramsArray != null) {
			for (Object o : paramsArray) {
				if (o != null && !isFilterObject(o)) {
					try {
						JSONObject jsonObject = (JSONObject) JSON.toJSON(o);
						if (businessIdField.contains(".")) {
							logEntity.setBusinessId(getBusinessId(jsonObject, businessIdField));
						} else {
							logEntity.setBusinessId(jsonObject.getString(businessIdField));
						}
						params.append(jsonObject).append(" ");
					} catch (Exception e) {
						log.error(e.getMessage());
					}
				}
			}
		}
		logEntity.setRequestBody(StringUtils.substring(params.toString().trim(), 0, 512));
	}

	/**
	 * 根据参数路径获取参数值
	 *
	 * @param jsonObject      传入json对象
	 * @param businessIdField 传入地址
	 * @return String 返回businessId
	 */
	private String getBusinessId(JSONObject jsonObject, String businessIdField) {
		String[] businessFiledArray = businessIdField.split("\\.");
		int length = businessFiledArray.length;
		String businessKey = "";
		JSONObject json = new JSONObject();
		for (int i = 0; i < length; i++) {
			if (i == length - 1) {
				businessKey = json.getString(businessFiledArray[i]);
				break;
			}
			json = jsonObject.getJSONObject(businessFiledArray[i]);
		}
		return businessKey;
	}

	@SuppressWarnings("rawtypes")
	public boolean isFilterObject(final Object o) {
		Class<?> clazz = o.getClass();
		if (clazz.isArray()) {
			return clazz.getComponentType().isAssignableFrom(MultipartFile.class);
		} else if (Collection.class.isAssignableFrom(clazz)) {
			Collection collection = (Collection) o;
			for (Object value : collection) {
				return value instanceof MultipartFile;
			}
		} else if (Map.class.isAssignableFrom(clazz)) {
			Map map = (Map) o;
			for (Object value : map.entrySet()) {
				Map.Entry entry = (Map.Entry) value;
				return entry.getValue() instanceof MultipartFile;
			}
		}
		return o instanceof MultipartFile || o instanceof HttpServletRequest || o instanceof HttpServletResponse
			|| o instanceof BindingResult;
	}

}
