package org.skyworth.ess.utils;

import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.activation.MimeType;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName FileIOUtils
 * @Description 文件工具类
 * @Date 2024/3/25 15:03
 */
public class FileIOUtils {

    // 将文件转换为FileItem
    public static MultipartFile getMultipartFile(File file) {
        FileItemFactory factory = new DiskFileItemFactory(16, null);
        // 媒体类型指定为pdf类型
        FileItem fileItem = factory.createItem(file.getName(), "application/pdf", false, file.getName());
        int bytesRead = 0;
        int len = 8192;
        byte[] buffer = new byte[len];
        FileInputStream fis = null;
        OutputStream os = null;
        try {
            fis = new FileInputStream(file);
            os = fileItem.getOutputStream();
            while ((bytesRead = fis.read(buffer, 0, len)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            fis.close();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return new CommonsMultipartFile(fileItem);
    }
}
