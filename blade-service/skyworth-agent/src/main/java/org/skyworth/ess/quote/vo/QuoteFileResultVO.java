/*
 * Copyright (c) 2023. Skyworth All rights reserved
 */

package org.skyworth.ess.quote.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.skyworth.ess.ota.vo.FileModelVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName QuoteFileResultVO
 * @Description
 * @Date 2024/3/25 14:04
 */
@Data
@ApiModel(value = "QuoteFileResultVO对象", description = "附件上传回写页面参数")
public class QuoteFileResultVO {

    @ApiModelProperty(value = "业务id")
    private Long businessId;

    @ApiModelProperty(value = "订单id")
    private Long orderId;

    @ApiModelProperty(value = "附件列表")
    private List<FileModelVO> quoteFileModelVOList;
}
