/*
 * Copyright (c) 2023. Skyworth All rights reserved
 */

package org.skyworth.ess.aspect;

import org.skyworth.ess.constant.ModelEnum;
import org.skyworth.ess.constant.OperateEnum;

import java.lang.annotation.*;

/**
 * agent操作日志
 *
 * @description:
 * @author: SDT50545
 * @since: 2023-11-07 17:27
 **/
@Target({ElementType.METHOD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OperationLog {
	/**
	 * 操作
	 */
	OperateEnum operate();

	/**
	 * 功能模块
	 */
	ModelEnum model();

	/**
	 * 业务主键
	 */
	String businessIdFiled() default "id";
}
