package org.skyworth.ess.quote.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.skyworth.ess.ota.vo.FileModelVO;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel(value = "QuoteFileDTO对象", description = "报价单文件上传参数")
public class QuoteFileDTO {

    @ApiModelProperty(value = "订单id")
    @NotNull(message = "订单id不能为空")
    private Long orderId;

    @ApiModelProperty(value = "业务id")
    private Long businessId;

    @ApiModelProperty(value = "报价单文件列表")
    List<FileModelVO> fileResultVOList;
}
