package org.skyworth.ess.utils;

import cn.hutool.core.net.URLDecoder;
import com.alibaba.cloud.commons.io.Charsets;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName MyFileConvertUtil
 * @Description
 * @Date 2024/4/1 19:58
 */
public class MyFileConvertUtil {

    protected static final Logger logger = LoggerFactory.getLogger(MyFileConvertUtil.class);

    /**
     * Url 转换 MultipartFile
     *
     * @param url
     * @param fileName
     * @return
     * @throws Exception
     */
    public static MultipartFile urlToMultipartFile(String url, String fileName) {
        logger.info("开始 url 转换 MultipartFile，url={} ,fileName={}", url, fileName);
        File file = null;
        MultipartFile multipartFile = null;
        try {
            HttpURLConnection httpUrl = (HttpURLConnection) new URL(url).openConnection();
            httpUrl.connect();
            logger.info("成功建立httpUrl连接" + httpUrl);
            file = inputStreamToFile(httpUrl.getInputStream(), fileName);
            multipartFile = fileToMultipartFile(file);
            File newFile = new File("/tmp/" + fileName + ".pdf");
            multipartFile.transferTo(newFile);
            multipartFile = FileIOUtils.getMultipartFile(newFile);
            httpUrl.disconnect();
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("urlToMultipartFile error: {}", e.getMessage());
        }
        logger.info("完成 url 转换 MultipartFile，url={} ,fileName={}", url, fileName);
        return multipartFile;
    }


    /**
     * InputStream 转 File
     *
     * @param ins
     * @param fileName
     * @return
     * @throws Exception
     */
    public static File inputStreamToFile(InputStream ins, String fileName) throws Exception {
        logger.info("开始 InputStream 转换 File,fileName={}", fileName);
        File file = Files.createTempFile(fileName, ".pdf").toFile();
        OutputStream os = new FileOutputStream(file);
        int bytesRead;
        int len = 8192;
        byte[] buffer = new byte[len];
        while ((bytesRead = ins.read(buffer, 0, len)) != -1) {
            os.write(buffer, 0, bytesRead);
        }
        os.close();
        ins.close();
        logger.info("完成 InputStream 转换 File,fileName={}", fileName);
        return file;
    }


    /**
     * File 转 MultipartFile
     *
     * @param file
     * @return
     */
    public static CommonsMultipartFile fileToMultipartFile(File file) {
        logger.info("fileToMultipartFile文件转换中：" + file.getAbsolutePath());
        FileItemFactory factory = new DiskFileItemFactory(16, null);
        FileItem item = factory.createItem(file.getName(), "application/pdf", true, file.getName());
        int bytesRead = 0;
        byte[] buffer = new byte[8192];
        try {
            FileInputStream fis = new FileInputStream(file);
            OutputStream os = item.getOutputStream();
            while ((bytesRead = fis.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            fis.close();
        } catch (IOException e) {
            e.printStackTrace();
            logger.error("fileToMultipartFile error: {}", e.getMessage());
        } finally {
            try {
                Files.deleteIfExists(file.toPath());
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return new CommonsMultipartFile(item);
    }

}