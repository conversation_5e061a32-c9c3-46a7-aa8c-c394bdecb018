package org.skyworth.ess.quote.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 设备报价信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("device_quote_info")
@ApiModel(value="DeviceQuoteInfo对象", description="设备报价信息")
public class DeviceQuoteInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    @ApiModelProperty(value = "订单id")
    @TableField("order_id")
    private Long orderId;

    @ApiModelProperty(value = "报价附件key")
    @TableField("quote_doc_biz_key")
    private Long quoteDocBizKey;

    @ApiModelProperty(value = "推送email地址")
    @TableField("to_email")
    private String toEmail;

    @ApiModelProperty(value = "邮件内容")
    @TableField("content")
    private String content;

    @ApiModelProperty(value = "收款证明文档key")
    @TableField("payment_confirm_doc_biz_key")
    private Long paymentConfirmDocBizKey;

    @ApiModelProperty(value = "创建人")
    @TableField("create_user")
    private Long createUser;

    @ApiModelProperty(value = "创建人账号")
    @TableField("create_user_account")
    private String createUserAccount;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人")
    @TableField("update_user")
    private Long updateUser;

    @ApiModelProperty(value = "更新人账号")
    @TableField("update_user_account")
    private String updateUserAccount;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除")
    @TableField("is_deleted")
    private Integer isDeleted;

    @ApiModelProperty(value = "状态")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "租户id")
    @TableField("tenant_id")
    private String tenantId;

    @ApiModelProperty(value = "创建部门")
    @TableField("create_dept")
    private Long createDept;

    @ApiModelProperty(value = "是否发送邮件 1：是；0：否")
    @TableField("send_email")
    private Integer sendEmail;

    @ApiModelProperty(value = "密送人")
    @TableField("bcc_mail")
    private String bccMail;

    @ApiModelProperty(value = "抄送人")
    @TableField("cc_mail")
    private String ccMail;

    @ApiModelProperty(value = "付款确认是否发送邮件 1:是；0：否")
    @TableField("payment_confirm_send_mail")
    private Integer paymentConfirmSendMail;

    @ApiModelProperty(value = "付款确认邮件抄送人，多个邮箱分割，最多5个")
    @TableField("payment_confirm_mail_to")
    private String paymentConfirmMailTo;

    @ApiModelProperty(value = "付款确认邮件密送人，多个邮箱分割，最多5个")
    @TableField("payment_confirm_mail_bcc")
    private String paymentConfirmMailBcc;

    @ApiModelProperty(value = "付款确认邮件内容")
    @TableField("payment_confirm_mail_content")
    private String paymentConfirmMailContent;


}
