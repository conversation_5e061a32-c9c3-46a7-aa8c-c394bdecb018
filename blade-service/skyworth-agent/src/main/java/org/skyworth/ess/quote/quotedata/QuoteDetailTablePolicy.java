package org.skyworth.ess.quote.quotedata;

import com.deepoove.poi.data.RowRenderData;
import com.deepoove.poi.policy.DynamicTableRenderPolicy;
import com.deepoove.poi.policy.TableRenderPolicy;
import org.apache.poi.xwpf.usermodel.*;

import java.util.List;


public class QuoteDetailTablePolicy extends DynamicTableRenderPolicy {

    // 报价单清单列表填充数据所在行数
    private static final int QUOTE_DETAIL_TABLE_START_ROW = 22;

    @Override
    public void render(XWPFTable table, Object data) throws Exception {
        if (null == data)
            return;
        QuoteDetailData detailData = (QuoteDetailData) data;

        List<RowRenderData> items = detailData.getItems();
        if (null != items) {
            table.removeRow(QUOTE_DETAIL_TABLE_START_ROW);
            for (RowRenderData item : items) {
                XWPFTableRow insertNewTableRow = table.insertNewTableRow(QUOTE_DETAIL_TABLE_START_ROW);
                // 创建5个单元格
                for (int i = 0; i < 5; i++) {
                    insertNewTableRow.createCell();
                }
                // 添加行数据
                TableRenderPolicy.Helper.renderRow(table.getRow(QUOTE_DETAIL_TABLE_START_ROW), item);
                // 渲染行数据
                List<XWPFTableCell> tableCells = insertNewTableRow.getTableCells();
                for (int i = 0; i < tableCells.size(); i++) {
                    XWPFTableCell cell = tableCells.get(i);
                    XWPFParagraph paragraph = cell.getParagraphs().get(0);
                    // 设置字体
                    paragraph.getRuns().get(0).setFontFamily("SimHei");
                    // 设置字体大小
                    paragraph.getRuns().get(0).setFontSize(9);
                    // 设置文字居中
                    if (i == tableCells.size() -2 || i == tableCells.size() -1) {
                        paragraph.setAlignment(ParagraphAlignment.RIGHT);
                    } else if (i == tableCells.size() -4) {
                        paragraph.setAlignment(ParagraphAlignment.LEFT);
                    } else {
                        paragraph.setAlignment(ParagraphAlignment.CENTER);
                    }
                }
            }
        }
    }

}