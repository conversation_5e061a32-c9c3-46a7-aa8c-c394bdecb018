package org.springblade.flow.core.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
/**
 * 编辑权限 的相关参数对象
 * <AUTHOR>
 */
@Data
public class PurviewDTO implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 订单表ID
	 */
	private String orderId;
	/**
	 * 任务编号名称
	 */
	@NotBlank(message = "taskName  cannot be empty.")
	private String taskName;
}
