package org.springblade.flow.core.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Map;

/**
 * 订单审批流程入参
 * <AUTHOR>
 */
@Data
public class OrderFlowDTO implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 业务绑定ID
	 */
	private String businessId;
	/**
	 * 任务编号ID
	 */
	@NotBlank(message = "taskId  cannot be empty.")
	private String taskId;
	/**
	 * 任务编号名称
	 */
	@NotBlank(message = "taskName  cannot be empty.")
	private String taskName;
	/**
	 * 是否通过代号
	 */
	@NotNull(message = "flag  cannot be empty.")
	private Boolean flag;
	/**
	 * 流程实例ID
	 */
	@NotBlank(message = "processInstanceId  cannot be empty.")
	private String processInstanceId;
	/**
	 * 流程参数
	 */
	private Map<String, Object> variables;
}
